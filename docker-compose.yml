  version: '3.8'

  services:
    redis:
      image: redis:7-alpine
      restart: unless-stopped
      ports:
        - "6379:6379"
      volumes:
        - redis_data:/data

    livekit:
      image: livekit/livekit-server:latest
      restart: unless-stopped
      ports:
        - "7880:7880"
        - "7881:7881"
        - "7882:7882/udp"
      environment:
        - LIVEKIT_REDIS_ADDRESS=redis:6379
        - LIVEKIT_API_KEY=devkey
        - LIVEKIT_API_SECRET=secret
      volumes:
        - ./livekit.yaml:/etc/livekit.yaml
      depends_on:
        - redis

    livekit-sip:
      image: livekit/sip:latest
      restart: unless-stopped
      ports:
        - "5060:5060/udp"
        - "10000-10100:10000-10100/udp"
      environment:
        - LIVEKIT_URL=ws://livekit:7880
        - LIVEKIT_API_KEY=devkey
        - LIVEKIT_API_SECRET=secret
        - REDIS_URL=redis://redis:6379
      environment:
      SIP_CONFIG_BODY: | 
           api_key: '${LIVEKIT_API_KEY}'
           api_secret: '${LIVEKIT_API_SECRET}'
           ws_url: 'ws://livekit:7880'
           redis:
           address: 'redis:6379'
           password: 'call-agent'
           sip_port: 5060
           rtp_port: 10000-10100
           use_external_ip: true
           logging:
           level: debug
      volumes:
        - ./sip.yaml:/etc/sip.yaml
      depends_on:
        - livekit
        - redis
      command: --config /etc/sip.yaml

    asterisk:
      image: andrius/asterisk:latest
      restart: unless-stopped
      ports:
        - "5061:5060/udp"
        - "10101-10200:10101-10200/udp"
      volumes:
        - ./asterisk/sip.conf:/etc/asterisk/sip.conf
        - ./asterisk/extensions.conf:/etc/asterisk/extensions.conf
        - ./asterisk/asterisk.conf:/etc/asterisk/asterisk.conf
      depends_on:
        - livekit-sip

    agent:
      build:
        context: ./agent
        dockerfile: Dockerfile
      restart: unless-stopped
      environment:
        - LIVEKIT_URL=ws://livekit:7880
        - LIVEKIT_API_KEY=devkey
        - LIVEKIT_API_SECRET=secret
      depends_on:
        - livekit
      volumes:
        - ./agent:/app

  volumes:
    redis_data:
