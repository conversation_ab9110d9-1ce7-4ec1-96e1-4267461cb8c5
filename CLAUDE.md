# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a minimal integration between LiveKit and Asterisk, demonstrating how to connect SIP phones to LiveKit voice agents. The system allows SIP callers to interact with AI-powered voice assistants through a phone call.

## Development Commands

### Starting the system
```bash
# Ensure OpenAI API key is set
echo "OPENAI_API_KEY=your_key" > .env

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f agent
docker-compose logs -f livekit
docker-compose logs -f asterisk
```

### Stopping and cleanup
```bash
docker-compose down
docker-compose down -v  # Remove volumes
```

### Agent development
```bash
# Work inside agent container
docker-compose exec agent bash

# Restart only agent after code changes
docker-compose restart agent
```

## Architecture

The system consists of 5 Docker services:

1. **Redis** (port 6379): Shared state storage
2. **LiveKit Server** (port 7880): WebRTC media server and room management
3. **LiveKit SIP** (port 5060): SIP-to-WebRTC bridge
4. **Asterisk** (port 5061): SIP server handling phone calls
5. **Agent** (Python): Voice assistant using OpenAI for STT/TTS/LLM

### Call Flow
1. SIP phone connects to Asterisk (port 5061, ext 1000/pass 1000)
2. Asterisk forwards calls to LiveKit SIP bridge (port 5060)
3. SIP bridge creates LiveKit room and invites agent
4. Agent joins room and handles voice interaction using OpenAI

### Key Configuration Files

- `livekit.yaml`: LiveKit server configuration (ports, Redis, API keys)
- `sip.yaml`: SIP bridge configuration (trunks, routing rules)
- `asterisk/extensions.conf`: Asterisk dialplan routing calls to LiveKit
- `asterisk/sip.conf`: SIP peer configuration
- `agent/agent.py`: Voice assistant implementation

### Agent Architecture

The agent uses LiveKit's VoiceAssistant with:
- Silero VAD for voice activity detection
- OpenAI STT for speech-to-text
- OpenAI LLM for conversation
- OpenAI TTS for text-to-speech

## Testing

Test SIP connection:
- Connect SIP phone to localhost:5061
- Username: 1000, Password: 1000
- Dial any number to interact with the voice agent

## Environment Variables

Required for agent:
- `OPENAI_API_KEY`: OpenAI API key for STT/TTS/LLM
- `LIVEKIT_URL`: WebSocket URL to LiveKit server
- `LIVEKIT_API_KEY`: API key for LiveKit authentication
- `LIVEKIT_API_SECRET`: API secret for LiveKit authentication