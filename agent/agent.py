from __future__ import annotations

import asyncio
import logging
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
    RunContext,
)
from livekit.plugins import openai, silero
from livekit.plugins.turn_detector.english import EnglishModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VoiceAssistant(Agent):
    """Modern voice assistant using current LiveKit patterns"""

    def __init__(self):
        super().__init__(
            instructions="""
            You are a helpful voice assistant. Keep responses brief and clear.
            You can help users with general questions and conversations.
            Be polite and professional at all times.
            """
        )

    @function_tool()
    async def get_current_time(self, ctx: RunContext):
        """Get the current time"""
        import datetime
        current_time = datetime.datetime.now().strftime("%I:%M %p")
        logger.info(f"Providing current time: {current_time}")
        return f"The current time is {current_time}"

    @function_tool()
    async def end_conversation(self, ctx: RunContext):
        """End the conversation when the user wants to stop"""
        logger.info("User requested to end conversation")
        await ctx.session.generate_reply(
            instructions="Say goodbye politely and end the conversation"
        )


async def entrypoint(ctx: JobContext):
    """
    Modern agent entrypoint using AgentSession
    """
    logger.info(f"Agent connecting to room: {ctx.room.name}")

    # Connect to the room
    await ctx.connect()

    # Create the voice assistant
    agent = VoiceAssistant()

    # Create and start the agent session with modern components
    session = AgentSession(
        turn_detection=EnglishModel(),  # Better turn detection
        vad=silero.VAD.load(),
        stt=openai.STT(),
        llm=openai.LLM(model="gpt-4o"),  # Use latest model
        tts=openai.TTS(),
    )

    # Start the session
    await session.start(agent=agent, room=ctx.room)

    # Wait for first participant
    participant = await ctx.wait_for_participant()
    logger.info(f"Participant joined: {participant.identity}")


if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            agent_name="voice-assistant",
        )
    )
